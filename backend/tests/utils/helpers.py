"""
Test helper utilities for the RDF Agent System.
"""
import json
import uuid
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import AsyncMock, MagicMock

from pydantic_ai.models.test import TestModel
from pydantic_ai.result import RunResult

from models.data_models import QueryRequest, QueryResponse


class TestDataGenerator:
    """Generate test data for various components."""
    
    @staticmethod
    def generate_document_metadata(doc_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate document metadata for testing."""
        return {
            "id": doc_id or str(uuid.uuid4()),
            "filename": "test_document.pdf",
            "upload_time": "2024-01-01T00:00:00Z",
            "file_size": 1024,
            "content_type": "application/pdf",
            "processing_status": "completed"
        }
    
    @staticmethod
    def generate_embedding_data(doc_id: str, chunk_count: int = 3) -> List[Dict[str, Any]]:
        """Generate embedding data for testing."""
        embeddings = []
        for i in range(chunk_count):
            embeddings.append({
                "id": f"{doc_id}_chunk_{i}",
                "document_id": doc_id,
                "chunk_index": i,
                "text": f"This is chunk {i} of the test document.",
                "embedding": [0.1] * 384,  # Mock embedding vector
                "metadata": {
                    "page": i + 1,
                    "section": f"Section {i + 1}"
                }
            })
        return embeddings
    
    @staticmethod
    def generate_ttl_data(entity_count: int = 5) -> str:
        """Generate TTL data for testing."""
        ttl_content = """
@prefix ex: <http://example.org/> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .

"""
        for i in range(entity_count):
            ttl_content += f"""
ex:Building{i} rdf:type ex:Building ;
    rdfs:label "Test Building {i}" ;
    ex:hasAddress ex:Address{i} ;
    ex:energyRating "A+" .

ex:Address{i} rdf:type ex:Address ;
    ex:street "{i * 100} Test Street" ;
    ex:city "Test City" ;
    ex:country "Test Country" .

"""
        return ttl_content
    
    @staticmethod
    def generate_sparql_query(query_type: str = "SELECT") -> str:
        """Generate SPARQL queries for testing."""
        if query_type == "SELECT":
            return """
PREFIX ex: <http://example.org/>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>

SELECT ?building ?label WHERE {
    ?building a ex:Building ;
              rdfs:label ?label .
}
"""
        elif query_type == "COUNT":
            return """
PREFIX ex: <http://example.org/>

SELECT (COUNT(?building) as ?count) WHERE {
    ?building a ex:Building .
}
"""
        else:
            return """
PREFIX ex: <http://example.org/>

ASK {
    ?building a ex:Building .
}
"""
    
    @staticmethod
    def generate_kafka_message(usecase: str = "document_processing") -> Dict[str, Any]:
        """Generate Kafka message for testing."""
        return {
            "id": str(uuid.uuid4()),
            "usecase": usecase,
            "timestamp": "2024-01-01T00:00:00Z",
            "data": {
                "filename": "test_document.pdf",
                "bucket": "test-bucket",
                "processing_type": "full"
            }
        }


class MockResponseBuilder:
    """Build mock responses for various services."""
    
    @staticmethod
    def build_minio_response(objects: List[str]) -> List[str]:
        """Build MinIO list objects response."""
        return objects
    
    @staticmethod
    def build_qdrant_search_response(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Build Qdrant search response."""
        return [
            {
                "id": result.get("id", str(uuid.uuid4())),
                "score": result.get("score", 0.9),
                "payload": result.get("payload", {}),
                "vector": result.get("vector", [0.1] * 384)
            }
            for result in results
        ]
    
    @staticmethod
    def build_graphdb_response(bindings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build GraphDB SPARQL response."""
        return {
            "head": {
                "vars": list(bindings[0].keys()) if bindings else []
            },
            "results": {
                "bindings": [
                    {
                        key: {"type": "literal", "value": str(value)}
                        for key, value in binding.items()
                    }
                    for binding in bindings
                ]
            }
        }
    
    @staticmethod
    def build_ai_agent_response(content: str, tool_calls: Optional[List[Dict]] = None) -> str:
        """Build AI agent response."""
        return content


class TestModelBuilder:
    """Build test models for AI agents."""
    
    @staticmethod
    def create_test_model_with_responses(responses: List[str]) -> TestModel:
        """Create a TestModel with predefined responses."""
        model = TestModel()
        for response in responses:
            model.add_response(response)
        return model
    
    @staticmethod
    def create_test_model_with_tool_calls(
        responses: List[str], 
        tool_calls: List[Dict[str, Any]]
    ) -> TestModel:
        """Create a TestModel with tool calls."""
        model = TestModel()
        for i, response in enumerate(responses):
            if i < len(tool_calls):
                # Add tool call simulation
                model.add_response(response, tool_calls=[tool_calls[i]])
            else:
                model.add_response(response)
        return model


class AssertionHelpers:
    """Helper methods for test assertions."""
    
    @staticmethod
    def assert_valid_uuid(value: str) -> bool:
        """Assert that a string is a valid UUID."""
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def assert_valid_sparql(query: str) -> bool:
        """Assert that a string is valid SPARQL (basic check)."""
        query_upper = query.upper().strip()
        return any(keyword in query_upper for keyword in ["SELECT", "ASK", "CONSTRUCT", "DESCRIBE"])
    
    @staticmethod
    def assert_embedding_vector(vector: List[float], expected_size: int = 384) -> bool:
        """Assert that a vector is a valid embedding."""
        return (
            isinstance(vector, list) and
            len(vector) == expected_size and
            all(isinstance(x, (int, float)) for x in vector)
        )
    
    @staticmethod
    def assert_ttl_format(content: str) -> bool:
        """Assert that content is in TTL format (basic check)."""
        return (
            "@prefix" in content and
            "rdf:type" in content or "a " in content
        )


class FileHelpers:
    """Helper methods for file operations in tests."""
    
    @staticmethod
    def create_test_pdf(path: Path, content: str = "Test PDF content") -> Path:
        """Create a minimal test PDF file."""
        # This is a very basic PDF structure for testing
        pdf_content = f"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length {len(content) + 20}
>>
stream
BT
/F1 12 Tf
72 720 Td
({content}) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
{300 + len(content)}
%%EOF"""
        path.write_text(pdf_content)
        return path
    
    @staticmethod
    def create_test_json(path: Path, data: Dict[str, Any]) -> Path:
        """Create a test JSON file."""
        path.write_text(json.dumps(data, indent=2))
        return path
    
    @staticmethod
    def create_test_markdown(path: Path, content: str) -> Path:
        """Create a test Markdown file."""
        path.write_text(content)
        return path
    
    @staticmethod
    def create_test_ttl(path: Path, content: str) -> Path:
        """Create a test TTL file."""
        path.write_text(content)
        return path


class AsyncTestHelpers:
    """Helper methods for async testing."""
    
    @staticmethod
    async def wait_for_condition(condition_func, timeout: float = 5.0, interval: float = 0.1) -> bool:
        """Wait for a condition to become true."""
        import asyncio
        
        elapsed = 0.0
        while elapsed < timeout:
            if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
                return True
            await asyncio.sleep(interval)
            elapsed += interval
        return False
    
    @staticmethod
    async def collect_async_results(async_generator, max_items: int = 100) -> List[Any]:
        """Collect results from an async generator."""
        results = []
        count = 0
        async for item in async_generator:
            results.append(item)
            count += 1
            if count >= max_items:
                break
        return results
