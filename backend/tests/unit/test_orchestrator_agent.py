"""
Unit tests for the Orchestrator Agent.
"""
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from pydantic_ai.models.test import TestModel

from agents.orchestrator_agent import OrchestratorAgent, OrchestratorContext
from models.data_models import QueryRequest, QueryResponse
from tests.utils.helpers import TestDataGenerator, MockResponseBuilder, TestModelBuilder


class TestOrchestratorAgent:
    """Test cases for OrchestratorAgent."""
    
    @pytest.fixture
    def test_model(self):
        """Create a test model for the orchestrator agent."""
        return TestModelBuilder.create_test_model_with_responses([
            "I'll help you find information about buildings with energy rating A+.",
            "Based on the search results, I found 3 buildings with A+ energy rating.",
            "The analysis shows that buildings with A+ rating are well distributed across the city."
        ])
    
    @pytest.fixture
    def orchestrator_agent(self, mock_dependencies, test_model):
        """Create an orchestrator agent with mocked dependencies."""
        with patch('agents.orchestrator_agent.OpenRouterProvider'):
            with patch('agents.orchestrator_agent.OpenAIChatModel', return_value=test_model):
                agent = OrchestratorAgent(mock_dependencies)
                return agent
    
    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, mock_dependencies):
        """Test orchestrator agent initialization."""
        with patch('agents.orchestrator_agent.OpenRouterProvider') as mock_provider:
            with patch('agents.orchestrator_agent.OpenAIChatModel') as mock_model:
                agent = OrchestratorAgent(mock_dependencies)
                
                # Verify provider was created with correct API key
                mock_provider.assert_called_once_with(
                    api_key=mock_dependencies.settings.ai.or_api_key
                )
                
                # Verify model was created with correct parameters
                mock_model.assert_called_once_with(
                    'openai/gpt-4.1-mini', 
                    provider=mock_provider.return_value
                )
                
                # Verify agent has required components
                assert agent.deps == mock_dependencies
                assert agent.rdf_agent is not None
                assert agent.rag_agent is not None
                assert agent.agent is not None
    
    @pytest.mark.asyncio
    async def test_process_query_natural_language(self, orchestrator_agent, mock_dependencies):
        """Test processing natural language query."""
        query_request = QueryRequest(
            query="Find all buildings with energy rating A+",
            query_type="natural_language",
            session_id="test-session"
        )
        
        # Mock RDF agent response
        mock_dependencies.graphdb_client.execute_sparql_query.return_value = {
            "results": {
                "bindings": [
                    {"building": {"value": "http://example.org/Building1"}},
                    {"building": {"value": "http://example.org/Building2"}},
                    {"building": {"value": "http://example.org/Building3"}}
                ]
            }
        }
        
        response = await orchestrator_agent.process_query(query_request)
        
        assert isinstance(response, QueryResponse)
        assert response.success is True
        assert response.query == query_request.query
        assert response.query_type == query_request.query_type
        assert response.session_id == query_request.session_id
        assert len(response.data) > 0
    
    @pytest.mark.asyncio
    async def test_process_query_sparql_direct(self, orchestrator_agent, mock_dependencies):
        """Test processing direct SPARQL query."""
        sparql_query = """
        PREFIX ex: <http://example.org/>
        SELECT ?building WHERE {
            ?building a ex:Building ;
                      ex:energyRating "A+" .
        }
        """
        
        query_request = QueryRequest(
            query=sparql_query,
            query_type="sparql",
            session_id="test-session"
        )
        
        # Mock GraphDB response
        mock_dependencies.graphdb_client.execute_sparql_query.return_value = {
            "results": {
                "bindings": [
                    {"building": {"value": "http://example.org/Building1"}},
                    {"building": {"value": "http://example.org/Building2"}}
                ]
            }
        }
        
        response = await orchestrator_agent.process_query(query_request)
        
        assert isinstance(response, QueryResponse)
        assert response.success is True
        assert response.query_type == "sparql"
        assert len(response.data) == 2
        
        # Verify GraphDB was called with the SPARQL query
        mock_dependencies.graphdb_client.execute_sparql_query.assert_called_once_with(sparql_query)
    
    @pytest.mark.asyncio
    async def test_process_query_with_rag(self, orchestrator_agent, mock_dependencies):
        """Test processing query that requires RAG search."""
        query_request = QueryRequest(
            query="What are the energy efficiency recommendations from the latest report?",
            query_type="natural_language",
            session_id="test-session"
        )
        
        # Mock RAG search results
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc1_chunk1",
                "score": 0.95,
                "payload": {
                    "text": "Energy efficiency can be improved by installing LED lighting.",
                    "document_id": "doc1",
                    "page": 1
                }
            },
            {
                "id": "doc1_chunk2", 
                "score": 0.88,
                "payload": {
                    "text": "Proper insulation reduces energy consumption by 30%.",
                    "document_id": "doc1",
                    "page": 2
                }
            }
        ]
        
        response = await orchestrator_agent.process_query(query_request)
        
        assert isinstance(response, QueryResponse)
        assert response.success is True
        assert "energy efficiency" in response.response.lower()
    
    @pytest.mark.asyncio
    async def test_process_query_error_handling(self, orchestrator_agent, mock_dependencies):
        """Test error handling in query processing."""
        query_request = QueryRequest(
            query="Invalid query that will cause an error",
            query_type="natural_language",
            session_id="test-session"
        )
        
        # Mock an error in the RDF agent
        mock_dependencies.graphdb_client.execute_sparql_query.side_effect = Exception("GraphDB connection error")
        
        response = await orchestrator_agent.process_query(query_request)
        
        assert isinstance(response, QueryResponse)
        assert response.success is False
        assert "error" in response.response.lower()
        assert response.error_message is not None
    
    @pytest.mark.asyncio
    async def test_orchestrator_context_creation(self, orchestrator_agent):
        """Test orchestrator context creation."""
        context = OrchestratorContext(
            session_id="test-session",
            user_query="Test query",
            query_type="natural_language",
            conversation_history=[
                {"role": "user", "content": "Previous question"},
                {"role": "assistant", "content": "Previous answer"}
            ]
        )
        
        assert context.session_id == "test-session"
        assert context.user_query == "Test query"
        assert context.query_type == "natural_language"
        assert len(context.conversation_history) == 2
    
    @pytest.mark.asyncio
    async def test_health_check(self, orchestrator_agent):
        """Test orchestrator agent health check."""
        health_status = await orchestrator_agent.health_check()
        assert isinstance(health_status, bool)
        assert health_status is True
    
    @pytest.mark.asyncio
    async def test_tool_registration(self, orchestrator_agent):
        """Test that tools are properly registered."""
        # Check that the agent has tools registered
        assert hasattr(orchestrator_agent.agent, '_tools')
        
        # The exact structure depends on PydanticAI implementation
        # This is a basic check that tools exist
        tools = getattr(orchestrator_agent.agent, '_tools', {})
        assert len(tools) > 0
    
    @pytest.mark.asyncio
    async def test_query_rdf_database_tool(self, orchestrator_agent, mock_dependencies):
        """Test the query_rdf_database tool."""
        # Mock the RDF agent process_query method
        mock_dependencies.graphdb_client.execute_sparql_query.return_value = {
            "results": {"bindings": [{"count": {"value": "5"}}]}
        }
        
        # This would typically be called internally by the agent
        # We're testing the tool function directly
        context = OrchestratorContext(
            session_id="test-session",
            user_query="How many buildings are there?",
            query_type="natural_language"
        )
        
        # Simulate tool call (this is internal to PydanticAI)
        # In a real scenario, this would be called by the agent
        result = await orchestrator_agent.rdf_agent.process_query(
            query="How many buildings are there?",
            query_type="natural_language",
            session_id="test-session"
        )
        
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_search_documents_tool(self, orchestrator_agent, mock_dependencies):
        """Test the search_documents tool."""
        # Mock the RAG agent search
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc1_chunk1",
                "score": 0.9,
                "payload": {"text": "Test document content"}
            }
        ]

        # Simulate tool call through RAG agent
        result = await orchestrator_agent.rag_agent.search_documents(
            query_text="test query",
            limit=5
        )

        assert result is not None
    
    @pytest.mark.asyncio
    async def test_conversation_history_handling(self, orchestrator_agent):
        """Test conversation history handling."""
        query_request = QueryRequest(
            query="Follow up question",
            query_type="natural_language",
            session_id="test-session",
            conversation_history=[
                {"role": "user", "content": "What is energy rating?"},
                {"role": "assistant", "content": "Energy rating is a measure of efficiency."}
            ]
        )
        
        response = await orchestrator_agent.process_query(query_request)
        
        assert isinstance(response, QueryResponse)
        assert response.session_id == "test-session"
        # The response should consider the conversation history
        assert response.response is not None
