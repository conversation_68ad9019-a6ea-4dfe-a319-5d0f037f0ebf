"""
Unit tests for the RAG Agent.
"""
import pytest
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch
from pydantic_ai.models.test import TestModel

from agents.rag_agent import RAGAgent, RAGContext
from tests.utils.helpers import TestDataGenerator, MockResponseBuilder, TestModelBuilder, AssertionHelpers


class TestRAGAgent:
    """Test cases for RAGAgent."""
    
    @pytest.fixture
    def test_model(self):
        """Create a test model for the RAG agent."""
        return TestModelBuilder.create_test_model_with_responses([
            "Based on the search results, I found relevant information about energy efficiency.",
            "The documents show that LED lighting can improve energy efficiency by 40%.",
            "According to the retrieved documents, proper insulation is crucial for energy savings."
        ])
    
    @pytest.fixture
    def rag_agent(self, mock_dependencies, test_model):
        """Create a RAG agent with mocked dependencies."""
        with patch('agents.rag_agent.OpenRouterProvider'):
            with patch('agents.rag_agent.OpenAIChatModel', return_value=test_model):
                with patch('agents.rag_agent.SentenceTransformer') as mock_transformer:
                    # Mock the embedding model
                    mock_transformer.return_value.encode.return_value = [0.1] * 384
                    agent = RAGAgent(mock_dependencies)
                    return agent
    
    @pytest.mark.asyncio
    async def test_rag_agent_initialization(self, mock_dependencies):
        """Test RAG agent initialization."""
        with patch('agents.rag_agent.OpenRouterProvider') as mock_provider:
            with patch('agents.rag_agent.OpenAIChatModel') as mock_model:
                with patch('agents.rag_agent.SentenceTransformer'):
                    agent = RAGAgent(mock_dependencies)
                    
                    # Verify provider was created with correct API key
                    mock_provider.assert_called_once_with(
                        api_key=mock_dependencies.settings.ai.or_api_key
                    )
                    
                    # Verify model was created with correct parameters
                    mock_model.assert_called_once_with(
                        'openai/gpt-4.1-mini', 
                        provider=mock_provider.return_value
                    )
                    
                    # Verify agent has required components
                    assert agent.deps == mock_dependencies
                    assert agent.agent is not None
    
    @pytest.mark.asyncio
    async def test_search_documents(self, rag_agent, mock_dependencies):
        """Test document search functionality."""
        query_text = "energy efficiency recommendations"
        
        # Mock Qdrant search response
        mock_search_results = MockResponseBuilder.build_qdrant_search_response([
            {
                "id": "doc1_chunk1",
                "score": 0.95,
                "payload": {
                    "text": "LED lighting can improve energy efficiency by up to 40%.",
                    "document_id": "doc1",
                    "page": 1,
                    "section": "Energy Efficiency"
                }
            },
            {
                "id": "doc1_chunk2",
                "score": 0.88,
                "payload": {
                    "text": "Proper insulation reduces energy consumption significantly.",
                    "document_id": "doc1",
                    "page": 2,
                    "section": "Insulation"
                }
            }
        ])
        
        mock_dependencies.qdrant_client.search.return_value = mock_search_results
        
        result = await rag_agent.search_documents(
            query_text=query_text,
            limit=5
        )
        
        assert result is not None
        assert isinstance(result, str)
        assert "energy efficiency" in result.lower()
    
    @pytest.mark.asyncio
    async def test_generate_embeddings(self, rag_agent):
        """Test embedding generation."""
        text = "This is a test document for embedding generation."
        
        # Mock the embedding model
        with patch.object(rag_agent, '_get_embedding') as mock_embed:
            mock_embed.return_value = [0.1] * 384
            
            embedding = await rag_agent._get_embedding(text)
            
            assert embedding is not None
            assert AssertionHelpers.assert_embedding_vector(embedding)
            assert len(embedding) == 384
    
    @pytest.mark.asyncio
    async def test_search_vector_database_tool(self, rag_agent, mock_dependencies):
        """Test the search_vector_database tool."""
        query_text = "sustainable building practices"
        
        # Mock Qdrant response
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc2_chunk1",
                "score": 0.92,
                "payload": {
                    "text": "Sustainable building practices include using renewable materials.",
                    "document_id": "doc2",
                    "metadata": {"category": "sustainability"}
                }
            }
        ]
        
        # Create context for tool call
        context = RAGContext(
            session_id="test-session",
            query_text=query_text,
            search_limit=5
        )
        
        # Test the tool functionality (simulating internal agent call)
        with patch.object(rag_agent, '_get_embedding') as mock_embed:
            mock_embed.return_value = [0.1] * 384
            
            result = await mock_dependencies.qdrant_client.search(
                query_vector=[0.1] * 384,
                limit=5,
                score_threshold=0.7
            )
            
            assert result is not None
            assert len(result) == 1
            assert result[0]["score"] == 0.92
    
    @pytest.mark.asyncio
    async def test_process_query_with_context(self, rag_agent, mock_dependencies):
        """Test processing query with retrieved context."""
        query = "What are the best practices for energy efficiency?"
        
        # Mock search results
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc1_chunk1",
                "score": 0.95,
                "payload": {
                    "text": "Best practices include LED lighting, proper insulation, and smart thermostats.",
                    "document_id": "doc1"
                }
            }
        ]
        
        result = await rag_agent.process_query(
            query=query,
            session_id="test-session"
        )
        
        assert result is not None
        assert isinstance(result, str)
        assert "energy efficiency" in result.lower()
    
    @pytest.mark.asyncio
    async def test_error_handling_qdrant_failure(self, rag_agent, mock_dependencies):
        """Test error handling when Qdrant fails."""
        # Mock Qdrant to raise an exception
        mock_dependencies.qdrant_client.search.side_effect = Exception("Qdrant connection error")
        
        result = await rag_agent.search_documents(
            query_text="test query",
            limit=5
        )
        
        # Should handle the error gracefully
        assert result is not None
        assert "error" in result.lower()
    
    @pytest.mark.asyncio
    async def test_error_handling_embedding_failure(self, rag_agent):
        """Test error handling when embedding generation fails."""
        with patch.object(rag_agent, '_get_embedding') as mock_embed:
            mock_embed.side_effect = Exception("Embedding model error")
            
            result = await rag_agent.search_documents(
                query_text="test query",
                limit=5
            )
            
            # Should handle the error gracefully
            assert result is not None
    
    @pytest.mark.asyncio
    async def test_rag_context_creation(self, rag_agent):
        """Test RAG context creation."""
        context = RAGContext(
            session_id="test-session",
            query_text="Test query",
            search_limit=10
        )
        
        assert context.session_id == "test-session"
        assert context.query_text == "Test query"
        assert context.search_limit == 10
    
    @pytest.mark.asyncio
    async def test_health_check(self, rag_agent):
        """Test RAG agent health check."""
        health_status = await rag_agent.health_check()
        assert isinstance(health_status, bool)
        assert health_status is True
    
    @pytest.mark.asyncio
    async def test_search_with_filters(self, rag_agent, mock_dependencies):
        """Test search with metadata filters."""
        query_text = "energy efficiency"
        
        # Mock filtered search results
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc1_chunk1",
                "score": 0.9,
                "payload": {
                    "text": "Energy efficiency in commercial buildings.",
                    "document_id": "doc1",
                    "category": "commercial"
                }
            }
        ]
        
        result = await rag_agent.search_documents(
            query_text=query_text,
            limit=5,
            filters={"category": "commercial"}
        )
        
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_search_score_threshold(self, rag_agent, mock_dependencies):
        """Test search with score threshold."""
        query_text = "building materials"
        
        # Mock search results with varying scores
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc1_chunk1",
                "score": 0.95,  # Above threshold
                "payload": {"text": "High quality building materials are essential."}
            },
            {
                "id": "doc2_chunk1", 
                "score": 0.6,   # Below threshold
                "payload": {"text": "Some other text about materials."}
            }
        ]
        
        result = await rag_agent.search_documents(
            query_text=query_text,
            limit=5,
            score_threshold=0.8
        )
        
        assert result is not None
        # Should only include results above threshold
    
    @pytest.mark.asyncio
    async def test_embedding_model_loading(self, rag_agent):
        """Test embedding model loading and initialization."""
        # Test that embedding model is properly initialized
        assert rag_agent.embedding_model is not None or hasattr(rag_agent, '_get_embedding')
    
    @pytest.mark.asyncio
    async def test_document_chunking_context(self, rag_agent, mock_dependencies):
        """Test handling of document chunks in search results."""
        query_text = "renewable energy"
        
        # Mock search results with chunk information
        mock_dependencies.qdrant_client.search.return_value = [
            {
                "id": "doc1_chunk1",
                "score": 0.9,
                "payload": {
                    "text": "Renewable energy sources include solar and wind.",
                    "document_id": "doc1",
                    "chunk_index": 0,
                    "page": 1
                }
            },
            {
                "id": "doc1_chunk2",
                "score": 0.85,
                "payload": {
                    "text": "Solar panels can reduce electricity costs by 70%.",
                    "document_id": "doc1", 
                    "chunk_index": 1,
                    "page": 1
                }
            }
        ]
        
        result = await rag_agent.search_documents(
            query_text=query_text,
            limit=5
        )
        
        assert result is not None
        assert "renewable energy" in result.lower()
    
    @pytest.mark.asyncio
    async def test_session_management(self, rag_agent):
        """Test session management in RAG agent."""
        session_id = "test-session-456"
        
        result1 = await rag_agent.process_query(
            query="First query about energy",
            session_id=session_id
        )
        
        result2 = await rag_agent.process_query(
            query="Second query about efficiency",
            session_id=session_id
        )
        
        # Both results should be associated with the same session
        assert result1 is not None
        assert result2 is not None
